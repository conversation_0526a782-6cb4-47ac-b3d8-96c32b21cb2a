export function useFetch() {
  const preFlight = async (
    url = 'https://live.douyin.com/c7e7be5bb512a22613ab45d9bedc07aa',
  ) => {
    return fetch(url, { method: 'GET' })
  }

  const getRealRoomId = async (rid: string) => {
    const url = 'https://live.douyin.com/webcast/room/web/enter/?'
    const params = new URLSearchParams({
      'aid': '6383',
      'live_id': '1',
      'device_platform': 'web',
      'language': 'zh-CN',
      'enter_from': 'web_live',
      'cookie_enabled': 'true',
      'screen_width': '1920',
      'screen_height': '1080',
      'browser_language': 'zh-CN',
      'browser_platform': 'MacIntel',
      'browser_name': 'Chrome',
      'browser_version': '108.0.0.0',
      'web_rid': rid,
      'Room-Enter-User-Login-Ab': '0',
      'is_need_double_stream': 'false',
    })
    const res = await fetch(url + params, {
      method: 'GET',
      credentials: 'same-origin',
    }).then(response => response.json())
    return res.data.data[0].id_str as string
  }

  return {
    preFlight,
    getRealRoomId,
  }
}
