import type { MonitorOptions } from '@/shared/types'

export function getRandom(min: number, max: number) {
  return Math.floor(Math.random() * (max - min) + min)
}

export function getStrMiddle(str: string, before: string, after: string) {
  const m = str.match(new RegExp(`${before}(.*?)${after}`))
  return m ? m[1] : false
}

export function saveLocalData(name: string, data: string) {
  localStorage.setItem(name, data)
}

export function getLocalData(name: string) {
  return localStorage.getItem(name)
}

export function deepCopy<T extends Record<string, any>>(v: T): T {
  return JSON.parse(JSON.stringify(v))
}

export function getClassStyle(
  dom: HTMLElement,
  attr: keyof CSSStyleDeclaration,
): string | CSSStyleDeclaration {
  // 获取dom的class里的css属性值
  const ie = !+'\v1' // 简单判断ie6~8
  if (attr === 'backgroundPosition') {
    // IE6~8不兼容backgroundPosition写法，识别backgroundPositionX/Y
    if (ie) {
      return (
        `${dom.style.backgroundPositionX} ${dom.style.backgroundPositionY}`
      )
    }
  }
  if (dom.style) {
    const styleValue = dom.style[attr]
    if (typeof styleValue === 'string') {
      return styleValue
    }
  }
  return document.defaultView!.getComputedStyle(dom, null)[attr] as
    | string
    | CSSStyleDeclaration
}

export function formatObj(
  obj: Partial<MonitorOptions>,
  objTemplate: Partial<MonitorOptions>,
) {
  const ret: Partial<MonitorOptions> = {}
  // 将obj格式化成objTemplate的属性格式，而obj的值不变，缺少的属性会增加上去
  for (const key in objTemplate) {
    if (key in obj) {
      if (
        Object.prototype.toString.call(objTemplate[key]) === '[object Object]'
      ) {
        const childRet = formatObj(obj[key], objTemplate[key])
        ret[key] = childRet
      }
      else {
        ret[key] = obj[key]
      }
    }
    else {
      ret[key] = objTemplate[key]
    }
  }
  // 去除多余属性
  for (const key in ret) {
    if (!(key in objTemplate)) {
      delete ret[key]
    }
  }
  return ret as MonitorOptions
}
