import { defaultOptions } from '../options'
import {
  deepCopy,
  formatObj,
  getLocalData,
  saveLocalData,
} from '@/global/utils'
import type { MonitorOptions, UserEntity } from '@/shared/types'

export function useOptions() {
  const options = ref(deepCopy(defaultOptions))
  const banList = ref<UserEntity[]>([])
  const vipList = ref<UserEntity[]>([])

  const LOCAL_NAME = 'monitor_options'
  const BAN_LIST = 'ban_list'
  const VIP_LIST = 'vip_list'

  let localOptions = JSON.parse(getLocalData(LOCAL_NAME) as string)
  if (Object.prototype.toString.call(localOptions) !== '[object Object]') {
    localOptions = deepCopy(defaultOptions)
  }
  options.value = localOptions
  options.value = formatObj(options.value, defaultOptions)

  if (getLocalData(BAN_LIST))
    banList.value = JSON.parse(getLocalData(BAN_LIST) as string)
  if (getLocalData(VIP_LIST))
    vipList.value = JSON.parse(getLocalData(VIP_LIST) as string)

  const preReadHook = (data: MonitorOptions) => {
    const scopedOptions: Partial<typeof data> = deepCopy(data)
    delete scopedOptions.auth
    return scopedOptions
  }

  const preSaveHook = (data: MonitorOptions) => {
    data = formatObj(data, options.value)
    data.auth = options.value.auth
    if (JSON.stringify(data) === JSON.stringify(options.value))
      throw new Error('same')
    options.value = data
  }

  watch(
    options,
    (n) => {
      saveLocalData(LOCAL_NAME, JSON.stringify(n))
    },
    { deep: true },
  )

  watch(
    banList,
    (n) => {
      saveLocalData(BAN_LIST, JSON.stringify(n))
    },
    { deep: true },
  )

  watch(
    vipList,
    (n) => {
      saveLocalData(VIP_LIST, JSON.stringify(n))
    },
    { deep: true },
  )

  return { options, banList, vipList, preSaveHook, preReadHook }
}
