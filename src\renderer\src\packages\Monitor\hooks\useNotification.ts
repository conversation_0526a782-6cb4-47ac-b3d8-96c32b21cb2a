import type { Ref } from 'vue'

export function useNotification(
  isShowDialog: Ref<boolean>,
  isShowOption: Ref<boolean>,
  isShowDialogAuth: Ref<boolean>,
) {
  const displayNotifyMessage = (
    title = '警告',
    message: string,
    type: 'warning' | 'error' | 'success' | 'info' = 'warning',
    duration = 3000,
  ) => {
    if (isShowDialog.value === true)
      isShowDialog.value = false
    if (isShowOption.value === true)
      isShowOption.value = false
    ElNotification({
      title,
      type,
      message,
      duration,
    })
  }

  onMounted(() => {
    // TODO further abstraction / optional
    // 监听fs错误
    window.addEventListener('fserror', () => {
      displayNotifyMessage('文件系统', '文件IO操作异常', 'error')
    })

    window.addEventListener('wsexists', () => {
      displayNotifyMessage(
        '连接已建立',
        '服务端连接已建立, 请勿重复操作',
        'warning',
      )
    })

    window.addEventListener('wsconnected', () => {
      displayNotifyMessage('认证成功', '成功连接到服务器', 'success')
    })

    window.addEventListener('wsrejected', () => {
      isShowDialogAuth.value = true
      displayNotifyMessage('认证失败', '请重新输入认证码', 'error', 5000)
    })

    window.addEventListener('norid', () => {
      // isShowDialogAuth.value = true;
      displayNotifyMessage('获取直播间信息失败', '请重试', 'error', 5000)
    })

    window.addEventListener('opexpsuccess', () => {
      displayNotifyMessage(
        '导出设置成功',
        '设置文件已存储到指定路径',
        'success',
      )
    })

    window.addEventListener('opexpfailed', () => {
      displayNotifyMessage('导出设置失败', '发生未知错误', 'error')
    })

    window.addEventListener('opimpsuccess', () => {
      displayNotifyMessage('导入设置成功', '设置已生效', 'success')
    })

    window.addEventListener('opimpnochange', () => {
      displayNotifyMessage('无设置项更改', '导入设置与当前设置相同')
    })

    window.addEventListener('opimpfailed', () => {
      displayNotifyMessage('导入设置失败', '发生未知错误', 'error')
    })
  })

  return { displayNotifyMessage }
}
