<script setup lang="ts">
import { useScroll } from '../../hooks/useScroll'
import Deafult from './templates/Default.vue'

import type {
  ChatMsg,
  MonitorOptions,
  TemplateRef,
  UserEntity,
} from '@/shared/types'

defineProps<{
  options: MonitorOptions
  danmakuList: ChatMsg[]
}>()

const emit = defineEmits<{
  (e: 'userOps', user: UserEntity): void
}>()

const dom_danmaku = ref<TemplateRef>(null)

const { isLock, onScroll, onScrollUpdate, goToScrollBottom } = useScroll()

onUpdated(() => {
  onScrollUpdate(dom_danmaku.value as TemplateRef)
})
onMounted(() => {
  dom_danmaku.value?.addEventListener('mousewheel', () => {
    onScroll(dom_danmaku.value as TemplateRef)
  })
  dom_danmaku.value?.addEventListener('touchmove', () => {
    onScroll(dom_danmaku.value as TemplateRef)
  })
  window.addEventListener('resize', () => {
    onScroll(dom_danmaku.value as TemplateRef)
  })
})

function passToIndex(user: UserEntity) {
  emit('userOps', user)
}
</script>

<template>
  <div
    ref="dom_danmaku"
    class="danmaku"
  >
    <Deafult
      v-for="item in danmakuList"
      :key="item.key"
      :data="item"
      @user-clicked="passToIndex"
    />
    <div
      v-show="isLock"
      class="gobottom"
      @click.stop="goToScrollBottom(dom_danmaku as TemplateRef)"
    >
      回到底部
    </div>
  </div>
</template>

<style lang="scss" scoped>
@import '@/global/styles/themes/index.scss';
.danmaku {
  height: 100%;
  width: 100;
  padding: 0 5px;
  box-sizing: border-box;
  overflow-x: hidden;
  overflow-y: auto;
  content-visibility: auto;
  .item {
    justify-content: flex-start;
    text-align: left;
  }
}
</style>
