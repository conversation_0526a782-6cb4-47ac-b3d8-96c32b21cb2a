<script setup lang="ts">
import type { ChatMsg, UserEntity } from '@/shared/types'

defineProps<{
  data: ChatMsg
}>()

const emit = defineEmits<{
  (e: 'userClicked', user: UserEntity): void
}>()

// 左键点击添加至特别关注
function passHandler(user: UserEntity) {
  emit('userClicked', user)
}
</script>

<template>
  <div>
    <div class="item">
      <span class="time_stamp">{{ data.dt }}</span>
      <span
        v-if="data.lvl !== '0'"
        class="item__level"
      >[Lv.{{ data.lvl }}]</span>
      <span
        class="item__name"
        @click.left.prevent="
          passHandler({
            userName: data.nn,
            userId: data.uid,
            userSecUid: data.secuid,
          })
        "
      >{{ data.nn }}:&nbsp;
      </span>
      <span class="item__txt">{{ data.txt }}</span>
    </div>
  </div>
</template>

<style lang="scss" scoped>
@import '@/global/styles/themes/index.scss';

.time_stamp {
  color: black;
  vertical-align: middle;
}
.item {
  vertical-align: middle;
  width: 100%;
  margin-bottom: 5px;
  &:first-child {
    margin-top: 5px;
  }
  > * {
    margin-right: 5px;
  }
  > :nth-last-child(1),
  > :nth-last-child(2) {
    margin-right: 0;
  }
  .item__level {
    vertical-align: middle;
    @include fontColor('nicknameColor');
  }
  .item__fans {
    width: 60px;
    position: relative;
    vertical-align: middle;
  }

  .item__avatar {
    vertical-align: middle;
    display: inline-block;
  }
  .item__name {
    vertical-align: middle;
    @include fontColor('nicknameColor');
  }
  .item__txt {
    vertical-align: middle;
    @include fontColor('txtColor');
  }
}
</style>
