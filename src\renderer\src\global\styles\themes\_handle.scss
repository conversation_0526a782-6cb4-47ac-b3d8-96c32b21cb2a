@import './_themes.scss';

//遍历主题map
@mixin themeify {
  @each $theme-name, $theme-map in $themes {
    //!global 把局部变量强升为全局变量
    $theme-map: $theme-map !global;
    //判断html的data-theme的属性值  #{}是sass的插值表达式
    //& sass嵌套里的父容器标识   @content是混合器插槽，像vue的slot
    [data-theme='#{$theme-name}'] & {
      @content;
    }
  }
}

//声明一个根据Key获取颜色的function
@function themed($key) {
  @return map-get($theme-map, $key);
}

@mixin backgroundColor($color) {
  @include themeify {
    background: themed($color);
  }
}

@mixin fontColor($color) {
  @include themeify {
    color: themed($color);
  }
}
