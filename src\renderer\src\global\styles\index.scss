// 全局样式
* {
  padding: 0;
  margin: 0;
}

html,
body,
#app {
  height: 100%;
  width: 100%;
  overflow: hidden;
  font-family: '微软雅黑';
}

/*滚动条样式*/
*::-webkit-scrollbar {
  width: 0px !important;
}
* {
  -ms-overflow-style: none;
  overflow: -moz-scrollbars-none;
}

.van-checkbox--horizontal {
  margin-bottom: 1px;
  margin-top: 1px;
}

.gobottom {
  cursor: pointer;
  position: sticky;
  left: 0;
  bottom: 0;
  width: 100%;
  height: 25px;
  background-color: rgba(255, 119, 0, 0.7);
  color: white;
  border-radius: 15px;
  text-align: center;
  line-height: 25px;
}
