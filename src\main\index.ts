import path from 'node:path'
import { exec } from 'node:child_process'
import {
  BrowserWindow,
  Menu,
  MenuItem,
  Notification,
  Tray,
  app,
  clipboard,
  dialog,
  ipcMain,
  nativeImage,
  shell,
} from 'electron'
import { autoUpdater } from 'electron-updater'
import log from 'electron-log'
import settings from 'electron-settings'

const is = {
  dev: !app.isPackaged,
}

// log.transports.file.level = 'info';
autoUpdater.logger = log

let win: BrowserWindow

function createWindow() {
  win = new BrowserWindow({
    minWidth: 1000,
    minHeight: 590,
    resizable: true,
    autoHideMenuBar: true,
    opacity: 1,
    frame: false,
    transparent: true,
    webPreferences: {
      preload: path.join(__dirname, '../preload/index.js'),
      // enableRemoteModule: true,
      nodeIntegration: true,
    },
  })
  if (is.dev && process.env.ELECTRON_RENDERER_URL) {
    win.loadURL(process.env.ELECTRON_RENDERER_URL)
  }
  else {
    win.loadFile(path.join(__dirname, '../app/index.html'))
  }
  // win.loadFile('entry/index.html');
  win.setAlwaysOnTop(true, 'normal')
}

if (process.platform === 'win32') {
  app.setAppUserModelId('com.astrisle.dyassistant')
}

const menu = new Menu()
menu.append(
  new MenuItem({
    label: 'Functions',
    submenu: [
      {
        label: 'DevTools',
        accelerator: 'Ctrl+Shift+I',
        click(_item, focusedWindow) {
          if (focusedWindow)
            focusedWindow.webContents.toggleDevTools()
        },
      },
    ],
  }),
)

Menu.setApplicationMenu(menu)

let tray = null
const icon = nativeImage.createFromPath(
  path.join(__dirname, '../../public/icon.ico'),
)
const contextMenu = Menu.buildFromTemplate([
  {
    label: '检查更新',
    click: () => {
      autoUpdater.checkForUpdates()
    },
    enabled: false,
  },
  { type: 'separator' },
  {
    label: '打开日志文件夹',
    click: () => {
      const dir = `${app.getPath('documents')}\\520-Logs`
      exec(`start "" "${dir}"`)
    },
  },
  { type: 'separator' },
  {
    label: '退出',
    click: () => {
      app.quit()
    },
  },
])

app.whenReady().then(async () => {
  tray = new Tray(icon)
  tray.setToolTip('520弹幕助手')
  tray.setContextMenu(contextMenu)

  createWindow()
  registerSettingEvents()

  app.on('activate', () => {
    if (BrowserWindow.getAllWindows().length === 0) {
      tray = new Tray(icon)
      tray.setToolTip('520弹幕助手')
      tray.setContextMenu(contextMenu)

      createWindow()
      registerSettingEvents()
    }
  })
})

async function registerSettingEvents() {
  if (await settings.has('size')) {
    const res = (await settings.get('size')) as {
      width: number
      height: number
    }
    win.setSize(res.width, res.height)
  }
  else {
    win.setSize(1000, 590)
  }

  if (await settings.has('position')) {
    const res = (await settings.get('position')) as {
      x: number
      y: number
    }
    win.setPosition(res.x, res.y)
  }
  else {
    win.setPosition(0, 0)
  }

  win.on('resized', () => {
    const size = win.getSize()
    settings.set('size', {
      width: size[0],
      height: size[1],
    })
  })

  win.on('moved', () => {
    const position = win.getPosition()
    settings.set('position', {
      x: position[0],
      y: position[1],
    })
  })
}

ipcMain.handle('get-doc-path', () => {
  return app.getPath('documents')
})

ipcMain.handle('get-desktop-path', () => {
  return app.getPath('desktop')
})

ipcMain.handle('request-update', () => {
  autoUpdater.checkForUpdates()
})

ipcMain.handle('get-settings-save-path', async () => {
  const filePath = await dialog.showSaveDialog(win, {
    title: '选择设置文件保存路径',
    defaultPath: 'settings.txt',
    filters: [
      {
        name: '文本文档',
        extensions: ['txt'],
      },
    ],
  })
  return filePath
})

ipcMain.handle('open', (_event, secuid) => {
  shell.openExternal(`https://www.douyin.com/user/${secuid}`)
})

ipcMain.handle('copy-user-link', (_event, secuid) => {
  clipboard.writeText(`https://www.douyin.com/user/${secuid}`)
})

ipcMain.handle('user-quit', () => {
  app.quit()
})

autoUpdater.on('update-not-available', (info) => {
  new Notification({
    title: '更新检查',
    body: `当前版本 V${info.version} 已是最新版本`,
  }).show()
})

autoUpdater.on('update-available', (info) => {
  new Notification({
    title: '发现新版本',
    body: `应用将会自动重启以更新至 V${info.version}`,
  }).show()
})

autoUpdater.on('error', (err) => {
  // Regular expression to match IPv4 address with port number
  const ipAndPortRegex = /(\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}):(\d{1,5})/g

  // Replace any matching IPv4 addresses and port information with 'address omitted'
  const sanitizedMessage = err.message.replace(
    ipAndPortRegex,
    'address omitted',
  )

  // Log the sanitized error message
  clipboard.writeText(sanitizedMessage)
  new Notification({
    title: '无法获取更新',
    body: '错误信息已复制到剪贴板',
  }).show()
})

autoUpdater.on('update-downloaded', () => {
  autoUpdater.quitAndInstall(true, true)
})

app.on('window-all-closed', () => {
  if (process.platform !== 'darwin') {
    app.quit()
  }
})
