import type { MonitorOptions } from '@/shared/types'

export const defaultOptions: MonitorOptions = {
  // 背景颜色(包含alpha)
  bgcolora: 'rgba(255,255,255,0.7)',
  // 纵向column 横向row
  moduleSize: {
    upper: 260,
    lower: 200,
  },
  // 数据阈值
  threshold: 1000,
  // 字号
  fontSize: 14,
  // 弹幕设置
  danmaku: {
    filter: {
      keywords: '', // 关键词
    },
  },
  // 礼物设置
  gift: {
    // 高亮价格
    highlightThreshold: 1000,
    matchRecipientName: '',
    showPrice: ['single', 'total'],
    showHighlight: ['name', 'price'],
    // 过滤项
    filter: {
      drop: 0,
      // 价格低于
      price: 500,
      // 礼物名称
      keywords: '',
    },
    edgeCases: '',
  },
  log: {
    dir: '',
  },
  auth: {
    code: '',
    target: '',
  },
}
