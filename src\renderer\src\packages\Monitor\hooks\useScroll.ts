export function useScroll() {
  const isLock = ref(false)

  const onScroll = (dom: HTMLElement | null) => {
    if (dom) {
      const element = dom
      // 是否有滚动条
      const isScroll = element.scrollHeight > element.clientHeight
      const isBottom
        = element.scrollHeight - element.scrollTop === element.clientHeight
      // 当且只当有滚动条 且不在底部的 时候，才锁住
      isLock.value = isScroll && !isBottom
    }
  }
  const onScrollUpdate = (dom: HTMLElement | null) => {
    if (!isLock.value && dom) {
      dom.scrollTop = dom.scrollHeight
    }
  }
  const goToScrollBottom = (dom: HTMLElement | null) => {
    if (dom) {
      dom.scrollTop = dom.scrollHeight
      isLock.value = false
    }
  }
  return { isLock, onScroll, onScrollUpdate, goToScrollBottom }
}
