import path from 'node:path'
import { defineConfig, externalizeDepsPlugin } from 'electron-vite'
import vue from '@vitejs/plugin-vue'

import AutoImport from 'unplugin-auto-import/vite'
import Components from 'unplugin-vue-components/vite'
import { ElementPlusResolver } from 'unplugin-vue-components/resolvers'

export default defineConfig({
  main: {
    build: {
      minify: true,
    },
    plugins: [externalizeDepsPlugin()],
  },
  preload: {
    build: {
      minify: true,
    },
    plugins: [externalizeDepsPlugin()],
  },
  renderer: {
    base: '',
    build: {
      outDir: 'out/app',
      minify: true,
    },
    plugins: [
      vue(),
      AutoImport({
        imports: ['vue'],
        resolvers: [ElementPlusResolver()],
      }),
      Components({
        resolvers: [ElementPlusResolver()],
      }),
    ],
    resolve: {
      alias: {
        '@': path.resolve(__dirname, 'src/renderer/src'),
      },
    },
    css: {
      preprocessorOptions: {
        scss: {
          // 添加公共样式
          additionalData: `@import "@/global/styles/themes/index.scss";
                          @import "@/global/styles/vars.scss";`,
          charset: false,
        },
      },
    },
  },
})
