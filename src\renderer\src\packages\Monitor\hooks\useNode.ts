import type { Ref } from 'vue'

import { useIpc } from './useIpc'
import type { MonitorOptions } from '@/shared/types'

const fs = window.main.fs
const { invokeMainEvent } = useIpc()

export function useNode(options: Ref<MonitorOptions>) {
  const getUserDocumentPath = () => invokeMainEvent('get-doc-path')
  const getUserDesktopPath = () => invokeMainEvent('get-desktop-path')
  const getFileSavePath = () => invokeMainEvent('get-settings-save-path')

  const updateLogPath = async () => {
    const dirLog = `${await getUserDocumentPath()}\\520-Logs`
    options.value.log.dir = dirLog
  }

  // 创建文件夹(如不存在)
  const createFileDir = async (fileDir: string) => {
    try {
      await fs.mkdir(fileDir, { recursive: true })
    }
    catch (err) {
      console.log('ERR::ROOT::MD - ', err, ', Target: ', fileDir)
      window.dispatchEvent(new Event('fserror'))
    }
  }

  // 记录弹幕信息到本地文件
  const logToLocalFile = async (
    str: string,
    pathObj: { path: string, fileName: string },
  ) => {
    const path = pathObj.path
    const fileName = pathObj.fileName
    const fullPath = `${path}\\${fileName}`

    try {
      await fs.appendFile(fullPath, `${str}\n`)
    }
    catch (err: any) {
      if (err.message.includes('ENOENT')) {
        await createFileDir(path)
        logToLocalFile(str, pathObj)
      }
      else {
        console.log('ERR::LOG::IO - ', err, ', Target:', pathObj)
        window.dispatchEvent(new Event('fserror'))
      }
    }
  }

  const overwriteFile = async (str: string, path: string) => {
    try {
      await fs.truncate(path)
    }
    catch (err: any) {
      if (!err.message.includes('ENOENT'))
        throw err
    }
    await fs.appendFile(path, str)
  }

  const logInit = async (dir: string, date: string, name: string) => {
    const fileDir = `${dir}\\${date}_${name}.txt`
    const timeStr = new Date().toLocaleTimeString(['en-GB'], {
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit',
    })
    const initLogMsg
      = `==================================================\n`
      + `[Renderer] Application launched, start logging...\n`
      + `Current time: ${
       date
       } ${
       timeStr
       }\n`
       + `==================================================\n`
    try {
      await fs.appendFile(fileDir, initLogMsg)
    }
    catch (err: any) {
      console.log('ERR::OM::INIT - ', err.message, ', Target: ', fileDir)
      window.dispatchEvent(new Event('fserror'))
    }
  }

  onMounted(async () => {
    await updateLogPath()
    let dirLog = options.value.log.dir
    const date = new Date()
    const dateStr
      = `${String(date.getFullYear())
       }-${
       String(date.getMonth() + 1)
       }-${
       String(date.getDate())}`

    dirLog = `${dirLog}\\${dateStr}`
    await createFileDir(dirLog);

    ['弹幕', '礼物', '特殊事件'].forEach(async (el) => {
      await logInit(dirLog, dateStr, el)
    })
  })

  return {
    getUserDocumentPath,
    getUserDesktopPath,
    updateLogPath,
    logToLocalFile,
    createFileDir,
    getFileSavePath,
    overwriteFile,
  }
}
