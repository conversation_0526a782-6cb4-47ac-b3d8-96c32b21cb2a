type SerializableValue = string | SerializableArray | SerializableObject

interface SerializableArray extends Array<SerializableValue> {}

interface SerializableObject {
  [key: string]: SerializableValue
}

class STT {
  serialize(obj: SerializableValue): string {
    if (typeof obj === 'string') {
      return this.escapeString(obj)
    }
    else if (Array.isArray(obj)) {
      return `${obj.map(this.serialize).join('/')}/`
    }
    else {
      const parts: string[] = []
      for (const key in obj) {
        parts.push(`${this.escapeString(key)}@=${this.serialize(obj[key])}`)
      }
      return `${parts.join('/')}/`
    }
  }

  deserialize<T extends SerializableObject = SerializableObject>(
    input: string,
  ): T {
    if (!input.endsWith('/')) {
      throw new Error('Invalid input string.')
    }

    const parts = input.slice(0, -1).split('/')
    if (parts.length === 1 && !parts[0].includes('@=')) {
      return this.unescapeString(parts[0]) as any as T
    }
    else {
      const result: SerializableObject = {}
      for (const part of parts) {
        const keyValue = part.split('@=')
        if (keyValue.length !== 2) {
          throw new Error('Invalid input string.')
        }

        const key = this.unescapeString(keyValue[0])
        const value = this.deserialize(`${keyValue[1]}/`)
        result[key] = value
      }
      return result as T
    }
  }

  escapeString(input: string): string {
    return input.replace(/@/g, '@A').replace(/\//g, '@S')
  }

  unescapeString(input: string): string {
    return input.replace(/@S/g, '/').replace(/@A/g, '@')
  }
}

export { STT }
