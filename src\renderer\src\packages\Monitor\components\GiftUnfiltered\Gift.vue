<script setup lang="ts">
import { useScroll } from '../../hooks/useScroll'
import Deafult from './templates/Default.vue'
import type { GiftMsg, MonitorOptions, TemplateRef } from '@/shared/types'

defineProps<{
  options: MonitorOptions
  giftListUnfiltered: GiftMsg[]
}>()

const { isLock, onScroll, onScrollUpdate, goToScrollBottom } = useScroll()
const dom_gift = ref<TemplateRef>(null)

onUpdated(() => {
  onScrollUpdate(dom_gift.value as TemplateRef)
})
onMounted(() => {
  dom_gift.value?.addEventListener('mousewheel', () => {
    onScroll(dom_gift.value as TemplateRef)
  })
  dom_gift.value?.addEventListener('touchmove', () => {
    onScroll(dom_gift.value as TemplateRef)
  })
  window.addEventListener('resize', () => {
    onScroll(dom_gift.value as TemplateRef)
  })
})
</script>

<template>
  <div
    ref="dom_gift"
    class="gift"
  >
    <Deafult
      v-for="item in giftListUnfiltered"
      :key="item.key"
      :data="item"
    />
    <div
      v-show="isLock"
      class="gobottom"
      @click.stop="goToScrollBottom(dom_gift as TemplateRef)"
    >
      回到底部
    </div>
  </div>
</template>

<style lang="scss" scoped>
.gift {
  height: 100%;
  padding: 0 5px;
  box-sizing: border-box;
  overflow-x: hidden;
  overflow-y: auto;
  content-visibility: auto;
  .item {
    justify-content: flex-start;
    text-align: left;
  }
}
</style>
