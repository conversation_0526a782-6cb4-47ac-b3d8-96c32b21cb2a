<script setup lang="ts">
import type { ChatMsg } from '@/shared/types'

defineProps<{
  data: ChatMsg
}>()
</script>

<template>
  <div class="item">
    <span class="time_stamp">{{ data.dt }}</span>
    <span
      v-if="data.lvl !== '0'"
      class="item__level"
    >[Lv.{{ data.lvl }}]</span>
    <span class="item__name">{{ data.nn }}:&nbsp;</span>
    <span class="item__txt">{{ data.txt }}</span>
  </div>
</template>

<style lang="scss" scoped>
@import '@/global/styles/themes/index.scss';
.time_stamp {
  color: black;
  vertical-align: middle;
}
.item {
  vertical-align: middle;
  width: 100%;
  margin-bottom: 5px;
  &:first-child {
    margin-top: 5px;
  }
  > * {
    margin-right: 5px;
  }
  > :nth-last-child(1),
  > :nth-last-child(2) {
    margin-right: 0;
  }
  .item__level {
    vertical-align: middle;
    @include fontColor('nicknameColor');
  }
  .item__fans {
    width: 60px;
    position: relative;
    vertical-align: middle;
  }

  .item__avatar {
    vertical-align: middle;
    display: inline-block;
  }
  .item__name {
    vertical-align: middle;
    @include fontColor('nicknameColor');
  }
  .item__txt {
    vertical-align: middle;
    @include fontColor('txtColor');
  }
}
.noble-day {
  background-color: rgba(255, 172, 88, 0.3);
  border-top: 1px solid #ffe4b8;
  border-bottom: 1px solid #ffe4b8;
}

.noble-night {
  background-color: rgb(55, 55, 55);
  border-top: 1px solid rgb(90, 90, 90);
  border-bottom: 1px solid rgb(90, 90, 90);
}
</style>
