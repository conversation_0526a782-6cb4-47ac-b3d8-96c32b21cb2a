export interface MonitorOptions {
  [index: string]: any
  bgcolora: string
  moduleSize: {
    upper: number
    lower: number
  }
  threshold: number
  fontSize: number
  danmaku: {
    filter: {
      keywords: string
    }
  }
  gift: {
    highlightThreshold: number
    matchRecipientName: string
    showPrice: string[]
    showHighlight: string[]
    filter: {
      drop: number
      price: number
      keywords: string
    }
    edgeCases: string
  }
  log: {
    dir: string
  }
  auth: {
    code: string
    target: string
  }
}

export interface MsgBase {
  [key: string]: string
  key: string
  dt: string
  nn: string
  uid: string
  secuid: string
  lvl: string
}

export interface ChatMsg extends MsgBase {
  txt: string
}

export interface GiftMsg extends MsgBase {
  gfid: string
  gfname: string
  gfcnt: string
  hits: string
  bid: string
  price: string
  to: string
}

export interface UserEnterMsg extends MsgBase {}

export interface UserEntity {
  userName: string
  userId: string
  userSecUid: string
  lastKnownAs?: string
}

export type TemplateRef = HTMLElement | null

export type MatchParams<T extends (...args: any) => any> =
  Parameters<T> extends (infer P)[] ? P : never
