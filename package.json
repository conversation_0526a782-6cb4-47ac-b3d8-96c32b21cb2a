{"name": "dy-flyby-proxy", "version": "3.4.4", "description": "https://www.douyu.com/520", "author": "星落", "license": "MIT", "main": "./out/main/index.js", "scripts": {"lint": "eslint .", "lint:fix": "eslint . --fix", "dev": "electron-vite dev --watch", "preview": "electron-vite preview", "build": "vue-tsc -b && electron-vite build && electron-builder --dir", "dist": "vue-tsc -b && electron-vite build && electron-builder --win --x64"}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "electron-log": "5.1.2", "electron-settings": "^4.0.2", "electron-updater": "^6.1.8", "element-plus": "^2.6.1", "vue": "^3.4.21"}, "devDependencies": {"@antfu/eslint-config": "^2.21.0", "@types/node": "^20.11.28", "@vitejs/plugin-vue": "^5.0.4", "electron": "^29.1.4", "electron-builder": "^24.13.3", "electron-vite": "^2.1.0", "eslint": "^9.4.0", "sass": "^1.72.0", "typescript": "^5.4.2", "unplugin-auto-import": "^0.17.5", "unplugin-vue-components": "^0.26.0", "vue-tsc": "^2.0.6"}, "build": {"appId": "com.astrisle.dyassistant", "productName": "520弹幕助手", "files": ["out", "public"], "win": {"icon": "public/icon.ico", "publish": [{"provider": "generic", "url": "http://************:11520/", "useMultipleRangeRequest": false}]}, "nsis": {"oneClick": false, "allowToChangeInstallationDirectory": true, "artifactName": "520弹幕助手 Setup ${version}.${ext}", "perMachine": false}}}