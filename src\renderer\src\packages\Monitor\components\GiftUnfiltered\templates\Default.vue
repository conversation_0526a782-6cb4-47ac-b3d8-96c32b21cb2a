<script setup lang="ts">
import type { GiftMsg } from '@/shared/types'

const props = defineProps<{
  data: GiftMsg
}>()

const giftMsg = computed(() => {
  return `${props.data.gfname}*${props.data.hits}`
})
</script>

<template>
  <div class="item">
    <span class="time_stamp">{{ data.dt }}</span>
    <span
      v-if="data.lvl !== '0'"
      class="item__level"
    >[Lv.{{ data.lvl }}]</span>
    <div class="item__name">
      {{ data.nn }}
    </div>
    <div class="item__cnt">
      {{ giftMsg }}
    </div>
  </div>
</template>

<style lang="scss" scoped>
@import '@/global/styles/themes/index.scss';

.time_stamp {
  color: black;
  vertical-align: middle;
}

.item {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  margin-bottom: 5px;
  justify-content: flex-start;
  text-align: left;

  &:first-child {
    margin-top: 5px;
  }
  > * {
    margin-right: 5px;
  }
  .item__gift {
    img {
      border-radius: 10%;
    }
  }
  .item__fans {
    width: 60px;
  }
  .item__level {
    vertical-align: middle;
    @include fontColor('nicknameColor');
  }
  .item__name {
    vertical-align: middle;
    @include fontColor('nicknameColor');
  }
  .item__cnt {
    vertical-align: middle;
    @include fontColor('contentColor');
  }
  .item__hits {
    @include fontColor('contentColor');
  }
}
</style>
