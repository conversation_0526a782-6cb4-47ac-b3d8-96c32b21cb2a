<script setup lang="ts">
import { ChatDotRound, Operation, Present } from '@element-plus/icons-vue'
import type { FormInstance, FormRules } from 'element-plus'
import Danmakuvip from '../components/DanmakuVIP/Danmaku.vue'
import Gift from '../components/Gift/Gift.vue'
import GiftUnfiltered from '../components/GiftUnfiltered/Gift.vue'

import Danmaku from '../components/Danmaku/Danmaku.vue'

import { useNormalStyle } from '../hooks/useNormalStyle'
import { useWebsocket } from '../hooks/useWebsocket'
import { useOptions } from '../hooks/useOptions'
import { useNotification } from '../hooks/useNotification'
import { useNode } from '../hooks/useNode'
import { useFetch } from '../hooks/useFetch'
import { useIpc } from '../hooks/useIpc'
import type { UserEntity } from '@/shared/types'

const VERSION = '3.4.4'

const isShowOption = ref(false)
const isShowDialog = ref(false)
const isShowDialogAuth = ref(false)
const isShowDialogClosed = ref(false)
const isPassedOneMinute = ref(false)
const isShowHiddenMenu = ref(false)
const isTreatingUserEntities = ref(false)
const isShowDialogConnectionLost = ref(false)
const isShowDialogUserOps = ref(false)

const tableSearch = ref('')

const selectedUser = ref<UserEntity | null>(null)

const secondaryTarget = ref('')

const reconnectTask = ref<ReturnType<typeof setTimeout> | null>(null)

const dialogIndex = ref(0)
const strToAdd = ref('')
const activeTab = ref('general')

const heightDiff = ref(0)
const heightUpper = ref(0)
const heightLower = ref(0)

const authFormRef = ref<FormInstance | null>(null)
const authFormContent = reactive({
  code: '',
  target: '',
})

const { options, banList, vipList, preSaveHook, preReadHook } = useOptions()
const { updateLogPath, logToLocalFile, getFileSavePath, overwriteFile }
  = useNode(options)
const {
  connectWs,
  closeWs,
  danmakuList,
  danmakuListVIP,
  giftList,
  giftListUnfiltered,
} = useWebsocket(options, vipList, banList, logToLocalFile)
const { fontSizeStyle, bgColorValue } = useNormalStyle(options)

useNotification(isShowDialog, isShowOption, isShowDialogAuth)

const { invokeMainEvent } = useIpc()

onMounted(async () => {
  // 窗口大小重新定位
  heightUpper.value = options.value.moduleSize.upper
  heightLower.value = options.value.moduleSize.lower
  window.addEventListener('resize', setNewHeight)
  if (options.value.auth.code === '' || options.value.auth.target === '') {
    isShowDialogAuth.value = true
  }
  else {
    await establish()
  }
})

window.addEventListener('stream-closed', () => {
  isShowDialogClosed.value = true
  setTimeout(() => {
    isPassedOneMinute.value = true
  }, 60000)
  // reconnectTask.value = setTimeout(async () => {
  //   isShowDialogClosed.value = false
  //   await establish()
  // }, 90000)
})

window.addEventListener('wserror', () => {
  isShowDialogConnectionLost.value = true
  reconnectTask.value = setTimeout(async () => {
    isShowDialogConnectionLost.value = false
    await establish()
  }, 15000)
})

function onChangeTarget() {
  if (options.value.auth.target !== secondaryTarget.value) {
    ElMessageBox.confirm(
      'Re-establish connection with chosen target?',
      'Confirm',
      {
        confirmButtonText: 'OK',
        cancelButtonText: 'Cancel',
      },
    )
      .then(async () => {
        closeWs()
        options.value.auth.target = secondaryTarget.value
        await establish()
      })
      .catch(() => {
        secondaryTarget.value = options.value.auth.target
      })
  }
}

async function onClickImmediatelyReconnect() {
  reconnectTask.value && clearTimeout(reconnectTask.value)
  await establish()
  isShowDialogClosed.value = false
  isShowDialogConnectionLost.value = false
}

function onClickQuitExplicitly() {
  invokeMainEvent('user-quit')
}

async function establish() {
  const { preFlight, getRealRoomId } = useFetch()
  await preFlight()
  try {
    const rrid = await getRealRoomId(options.value.auth.target)
    connectWs(rrid, options.value.auth.code)
  }
  catch (err) {
    window.dispatchEvent(new Event('norid'))
    console.log('ERR::INIT::FETCH - ', err)
  }
}

const ridOptions = [
  {
    value: '520hz001',
    label: '一厅',
    disabled: false,
  },
  {
    value: '520hz002',
    label: '二厅',
    disabled: true,
  },
]

function validateCode(_: any, value: any, callback: any) {
  if (value === '') {
    callback(new Error('请输入认证码'))
  }
  else if (!isValidMd5Hash(value)) {
    callback(new Error('认证码格式不正确, 请重新输入'))
  }
  else if (isValidMd5Hash(value)) {
    return true
  }
}

function validateTarget(_: any, value: any, callback: any) {
  if (value === '') {
    callback(new Error('请选择目标'))
  }
  else {
    return true
  }
}

const rules = ref<FormRules>({
  code: [{ validator: validateCode, trigger: 'blur' }],
  target: [
    {
      validator: validateTarget,
      trigger: 'blur',
    },
  ],
})

function isValidMd5Hash(input: string) {
  const md5HashRegex = /^[a-f0-9]{32}$/
  return md5HashRegex.test(input)
}

function submitAuthForm(formEl: FormInstance | null) {
  if (!formEl)
    return
  formEl.validate((valid: boolean) => {
    if (valid) {
      ElMessageBox.confirm(
        '请注意, 提交认证一旦完成, 监听目标无法再做修改, 请确保已经选择了正确的监听目标',
        '提示',
        {
          confirmButtonText: '提交',
          cancelButtonText: '返回',
          confirmButtonClass: 'is-plain',
          type: 'warning',
        },
      )
        .then(async () => {
          isShowDialogAuth.value = false
          options.value.auth.code = authFormContent.code
          options.value.auth.target = authFormContent.target
          await establish()
        })
        .catch(() => {})
    }
    else {
      authFormContent.code = ''
    }
  })
}

function onClickMonitor() {
  isShowOption.value = true
}

function showUserOps(user: UserEntity) {
  selectedUser.value = user
  isShowDialogUserOps.value = true
}

// 一键开盒
function whoAreYou(user: UserEntity | null) {
  // invokeMainEvent('open', userSecUid);
  isShowDialogUserOps.value = false
  if (!user)
    return
  invokeMainEvent('copy-user-link', user.userSecUid)
  ElNotification({
    title: '已复制',
    message: '已复制该用户主页链接',
    type: 'success',
    duration: 3000,
  })
}

function showDialog(index: number) {
  if (index === 0 || index === 3 || index === 4)
    isTreatingUserEntities.value = false
  else isTreatingUserEntities.value = true
  dialogIndex.value = index
  isShowDialog.value = true
}

function dialogGetOptions(index: number) {
  return [
    options.value.danmaku.filter.keywords,
    banList.value,
    vipList.value,
    // options.value.danmaku.filter.nicknames,
    // options.value.danmaku.vip,
    options.value.gift.filter.keywords,
    options.value.gift.edgeCases,
  ][index]
}

function dialogUpdateOptions(index: number, res: string | UserEntity[]) {
  if (typeof res === 'string') {
    switch (index) {
      case 0:
        options.value.danmaku.filter.keywords = res
        break
      case 3:
        options.value.gift.filter.keywords = res
        break
      case 4:
        options.value.gift.edgeCases = res
        break
      default:
        break
    }
  }
  else {
    switch (index) {
      case 1:
        // 屏蔽名单
        banList.value = res
        break
      case 2:
        vipList.value = res
        break
      default:
        break
    }
  }
}

const dialogData = computed(() => {
  const dataUnfiltered = parseDialogData(dialogIndex.value)
  if (dataUnfiltered && (dialogIndex.value === 1 || dialogIndex.value === 2)) {
    return dataUnfiltered.filter((el) => {
      return (
        // @ts-expect-error - el is UserEntity
        el.userName.includes(tableSearch.value)
        // @ts-expect-error - same as above
        || el.userId.includes(tableSearch.value)
      )
    })
  }
  else {
    return dataUnfiltered
  }
})

function parseDialogData(index: number) {
  const res = dialogGetOptions(index)
  if (typeof res === 'string' && res !== '') {
    return res.split(' ').map((el) => {
      return {
        name: el,
      }
    })
  }
  else if (Array.isArray(res) && res.length !== 0) {
    return res
  }
  else {
    return undefined
  }
}

function handleKeyWordDelete(index: number, item: string | UserEntity) {
  if (typeof item === 'string') {
    const orig = dialogGetOptions(index) as string
    dialogUpdateOptions(
      index,
      orig
        .split(' ')
        .filter(e => e !== item)
        .join(' '),
    )
  }
  else {
    switch (index) {
      case 1:
        dialogUpdateOptions(
          index,
          banList.value.filter(
            e =>
              e.userName !== item.userName
              || e.userId !== item.userId
              || e.userSecUid !== item.userSecUid,
          ),
        )
        break
      case 2:
        dialogUpdateOptions(
          index,
          vipList.value.filter(
            e =>
              e.userName !== item.userName
              || e.userId !== item.userId
              || e.userSecUid !== item.userSecUid,
          ),
        )
        break
    }
  }
}

function handleKeyWordAdd(index: number) {
  if (dialogGetOptions(index) === '') {
    dialogUpdateOptions(index, strToAdd.value.trim())
  }
  else {
    dialogUpdateOptions(
      index,
      `${dialogGetOptions(index)} ${strToAdd.value.trim()}`,
    )
  }
  strToAdd.value = ''
}

function setNewHeight() {
  heightDiff.value = document.documentElement.clientHeight - 590
  heightUpper.value = 260 + heightDiff.value / 2
  heightLower.value = 200 + heightDiff.value / 2
  options.value.moduleSize.upper = heightUpper.value
  options.value.moduleSize.lower = heightLower.value
}

async function saveOptionsWithDialog() {
  const winPath = await getFileSavePath()
  if (winPath.canceled)
    return
  const localOptions = preReadHook(options.value)
  const optionsStr = JSON.stringify(localOptions, null, 2)
  try {
    await overwriteFile(optionsStr, winPath.filePath)
    window.dispatchEvent(new Event('opexpsuccess'))
  }
  catch (err: any) {
    window.dispatchEvent(new Event('opexpfailed'))
    console.log('ERR::OP::EXP - ', err.message, ', Target: ', winPath.filePath)
  }
}

async function readOptionsFromUpload(res: any) {
  const resStr = await res.file.text()
  try {
    const resObj = JSON.parse(resStr)
    preSaveHook(resObj)
    await updateLogPath()
    window.dispatchEvent(new Event('opimpsuccess'))
  }
  catch (err: any) {
    if (err.message === 'same')
      window.dispatchEvent(new Event('opimpnochange'))
    else window.dispatchEvent(new Event('opimpfailed'))
    console.log('ERR::OP::IMP - ', err.message)
  }
}

function findInUserEntities(target: UserEntity[], toMatch: UserEntity) {
  return target.some(
    el =>
      el.userName === toMatch.userName
      || el.userId === toMatch.userId
      || el.userSecUid === toMatch.userSecUid,
  )
}

function addToVIP(user: UserEntity | null) {
  isShowDialogUserOps.value = false
  if (!user)
    return
  if (findInUserEntities(vipList.value, user)) {
    ElMessageBox.alert(`${user.userName} 已存在于特别关注中`, '用户已存在', {
      confirmButtonText: '确定',
      closeOnClickModal: true,
      confirmButtonClass: 'is-plain',
      type: 'error',
    })
      .then(() => {})
      .catch(() => {})
  }
  else if (findInUserEntities(banList.value, user)) {
    ElMessageBox.alert(
      `${user.userName} 已存在于屏蔽名单中, 请将其先从屏蔽名单中移除`,
      '用户已存在',
      {
        confirmButtonText: '确定',
        closeOnClickModal: true,
        confirmButtonClass: 'is-plain',
        type: 'error',
      },
    )
      .then(() => {})
      .catch(() => {})
  }
  else {
    ElMessageBox.confirm(
      `确认添加 ${user.userName} 到特别关注？`,
      '确认操作',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        confirmButtonClass: 'is-plain',
        type: 'warning',
      },
    )
      .then(() => {
        vipList.value.push(user)
      })
      .catch(() => {})
  }
}

function addToBan(user: UserEntity | null) {
  isShowDialogUserOps.value = false
  if (!user)
    return
  if (findInUserEntities(banList.value, user)) {
    ElMessageBox.alert(
      `${user.userName} 已存在于屏蔽名单中, 请将其先从屏蔽名单中移除`,
      '用户已存在',
      {
        confirmButtonText: '确定',
        closeOnClickModal: true,
        confirmButtonClass: 'is-plain',
        type: 'error',
      },
    )
      .then(() => {})
      .catch(() => {})
  }
  else if (findInUserEntities(vipList.value, user)) {
    ElMessageBox.alert(`${user.userName} 已存在于特别关注中`, '用户已存在', {
      confirmButtonText: '确定',
      closeOnClickModal: true,
      confirmButtonClass: 'is-plain',
      type: 'error',
    })
      .then(() => {})
      .catch(() => {})
  }
  else {
    ElMessageBox.confirm(
      `确认添加 ${user.userName} 到屏蔽名单？`,
      '确认操作',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        confirmButtonClass: 'is-plain',
        type: 'warning',
      },
    )
      .then(() => {
        banList.value.push(user)
      })
      .catch(() => {})
  }
}
</script>

<template>
  <el-container style="-webkit-app-region: drag">
    <el-main>
      <el-row
        class="mb-1"
        :gutter="5"
      >
        <el-col :span="14">
          <el-card class="bg">
            <div class="monitor">
              <Danmaku
                :style="{ height: `${heightUpper}px` }"
                :options="options"
                :danmaku-list="danmakuList"
                @user-ops="showUserOps"
              />
            </div>
          </el-card>
        </el-col>
        <el-col :span="10">
          <el-card class="bg">
            <div
              class="monitor"
              @click.right.prevent="onClickMonitor"
            >
              <Gift
                :style="{ height: `${heightUpper}px` }"
                :options="options"
                :gift-list="giftList"
              />
            </div>
          </el-card>
        </el-col>
      </el-row>
      <el-row :gutter="5">
        <el-col :span="14">
          <el-card class="bg no-drag">
            <div
              class="monitor"
              @click.right.prevent="onClickMonitor"
            >
              <Danmakuvip
                :style="{ height: `${heightLower}px` }"
                :options="options"
                :danmaku-list="danmakuListVIP"
              />
            </div>
          </el-card>
        </el-col>
        <el-col :span="10">
          <el-card class="bg no-drag">
            <div
              class="monitor"
              @click.right.prevent="onClickMonitor"
            >
              <GiftUnfiltered
                :style="{ height: `${heightLower}px` }"
                :options="options"
                :gift-list-unfiltered="giftListUnfiltered"
              />
            </div>
          </el-card>
        </el-col>
      </el-row>
    </el-main>
  </el-container>
  <el-drawer
    v-model="isShowOption"
    direction="ltr"
    size="33%"
    style="user-select: none"
    modal-class="no-drag"
    title="设置"
    :show-close="false"
    :with-header="false"
  >
    <el-tabs v-model="activeTab">
      <el-tab-pane name="general">
        <template #label>
          <el-icon><Operation /></el-icon>
          <span>通用</span>
        </template>
        <el-form>
          <el-form-item label="面板背景颜色 & 不透明度">
            <el-color-picker
              v-model="options.bgcolora"
              show-alpha
            />
          </el-form-item>
          <el-form-item label="字号(12-30)">
            <el-input-number
              v-model="options.fontSize"
              :min="12"
              :max="30"
              controls-position="right"
              :step-strictly="true"
            />
          </el-form-item>
          <el-form-item label="面板显示上限">
            <el-input-number
              v-model="options.threshold"
              :step="500"
              :min="500"
              :max="5000"
              controls-position="right"
              :step-strictly="true"
            />
          </el-form-item>
          <el-form-item label="设置迁移">
            <el-upload
              class="mr-2"
              action="#"
              accept=".txt"
              :http-request="readOptionsFromUpload"
              :show-file-list="false"
              disabled
            >
              <template #trigger>
                <el-button
                  plain
                  disabled
                >
                  导入设置
                </el-button>
              </template>
            </el-upload>
            <span>|</span>
            <el-button
              class="ml-2"
              plain
              disabled
              @click="saveOptionsWithDialog"
            >
              导出设置
            </el-button>
          </el-form-item>
          <el-form-item
            v-if="isShowHiddenMenu"
            label="监听目标"
          >
            <el-select
              v-model="secondaryTarget"
              placeholder="Choose..."
              @change="onChangeTarget()"
            >
              <el-option
                v-for="item in ridOptions"
                :key="item.label"
                :label="item.label"
                :value="item.value"
                :disabled="item.disabled"
                :default-first-option="false"
              />
            </el-select>
          </el-form-item>
        </el-form>
      </el-tab-pane>
      <el-tab-pane name="chat">
        <template #label>
          <el-icon><ChatDotRound /></el-icon>
          <span>弹幕</span>
        </template>
        <el-form>
          <el-form-item label="屏蔽关键词">
            <el-button
              plain
              :loading="isShowDialog"
              @click="showDialog(0)"
            >
              管理
            </el-button>
          </el-form-item>
          <el-form-item label="屏蔽用户">
            <el-button
              plain
              :loading="isShowDialog"
              @click="showDialog(1)"
            >
              管理
            </el-button>
          </el-form-item>
          <el-form-item label="特别关注">
            <el-button
              plain
              :loading="isShowDialog"
              @click="showDialog(2)"
            >
              管理
            </el-button>
          </el-form-item>
        </el-form>
      </el-tab-pane>
      <el-tab-pane name="gift">
        <template #label>
          <el-icon><Present /></el-icon>
          <span>礼物</span>
        </template>
        <el-form>
          <el-form-item label="监听名字">
            <el-input
              v-model="options.gift.matchRecipientName"
              placeholder="抖音名字"
              size="default"
              clearable
            />
          </el-form-item>
          <el-form-item label="显示礼物价值">
            <el-checkbox-group v-model="options.gift.showPrice">
              <el-checkbox label="single">
                单个
              </el-checkbox>
              <el-checkbox label="total">
                总值
              </el-checkbox>
            </el-checkbox-group>
          </el-form-item>
          <el-form-item label="显示高亮">
            <el-checkbox-group v-model="options.gift.showHighlight">
              <el-checkbox label="name">
                名字匹配
              </el-checkbox>
              <el-checkbox label="price">
                阈值匹配
              </el-checkbox>
            </el-checkbox-group>
          </el-form-item>
          <el-form-item label="一级过滤阈值">
            <el-input-number
              v-model="options.gift.filter.drop"
              :min="0"
              :max="500"
              :step="1"
              controls-position="right"
            />
          </el-form-item>
          <el-form-item label="二级过滤阈值">
            <el-input-number
              v-model="options.gift.filter.price"
              :min="0"
              :max="30000"
              :step="1"
              controls-position="right"
            />
          </el-form-item>
          <el-form-item label="高亮阈值">
            <el-input-number
              v-model="options.gift.highlightThreshold"
              :min="0"
              :max="30000"
              :step="1"
              controls-position="right"
            />
          </el-form-item>
          <el-form-item label="屏蔽关键词">
            <el-button
              plain
              :loading="isShowDialog"
              @click="showDialog(3)"
            >
              管理
            </el-button>
          </el-form-item>
          <el-form-item label="特殊礼物">
            <el-button
              plain
              :loading="isShowDialog"
              @click="showDialog(4)"
            >
              管理
            </el-button>
          </el-form-item>
        </el-form>
      </el-tab-pane>
    </el-tabs>
    <template #footer>
      <div class="text-xs flex-auto">
        520弹幕助手 Ver. {{ VERSION }}
      </div>
      <div class="text-xs flex-auto">
        Copyright © 2021-2024 星落
      </div>
      <div class="text-xs flex-auto">
        All rights reserved
      </div>
    </template>
  </el-drawer>
  <el-dialog
    v-model="isShowDialogUserOps"
    title="用户详情"
  >
    <el-form>
      <el-form-item label="昵称">
        {{ selectedUser?.userName }}
      </el-form-item>
      <el-form-item label="抖音号">
        {{ selectedUser?.userId }}
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button
        type="primary"
        @click="addToVIP(selectedUser)"
      >
        特别关注
      </el-button>
      <el-button
        type="danger"
        @click="addToBan(selectedUser)"
      >
        屏蔽
      </el-button>
      <el-button
        type="info"
        @click="whoAreYou(selectedUser)"
      >
        主页链接
      </el-button>
    </template>
  </el-dialog>
  <el-dialog
    v-model="isShowDialog"
    top="10vh"
  >
    <el-table
      v-if="isTreatingUserEntities"
      :data="dialogData"
      max-height="330"
      stripe
    >
      <el-table-column
        prop="userName"
        label="用户名"
      />
      <el-table-column
        prop="lastKnownAs"
        label="曾用名"
      />
      <el-table-column
        prop="userId"
        label="抖音号"
      />
      <el-table-column
        v-if="false"
        prop="userSecUid"
        label="SecUid"
      />
      <el-table-column fixed="right">
        <template #header>
          <el-input
            v-model="tableSearch"
            size="small"
            placeholder="搜索..."
          />
        </template>
        <!-- eslint-disable vue/no-unused-vars vue/no-parsing-error -->
        <template #default="scope">
          <el-button
            type="text"
            size="small"
            @click="
              handleKeyWordDelete(dialogIndex, {
                userName: <string>scope.row.userName,
                userId: <string>scope.row.userId,
                userSecUid: <string>scope.row.userSecUid,
              }
            )
            "
          >
            移除
          </el-button>
        </template>
        <!-- eslint-enable vue/no-unused-vars vue/no-parsing-error -->
      </el-table-column>
    </el-table>
    <el-table
      v-else
      :data="dialogData"
      max-height="330"
      stripe
    >
      <el-table-column prop="name" />
      <el-table-column
        fixed="right"
        label="操作"
        width="80"
      >
        <template #default="scope">
          <el-button
            type="text"
            size="small"
            @click="handleKeyWordDelete(dialogIndex, scope.row.name)"
          >
            移除
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <template
      v-if="!isTreatingUserEntities"
      #footer
    >
      <el-input
        v-model="strToAdd"
        class="w-32 mr-3 align-middle"
        placeholder="新关键词"
      />
      <el-button
        class="align-middle"
        type="primary"
        primary
        @click="handleKeyWordAdd(dialogIndex)"
      >
        添加
      </el-button>
    </template>
  </el-dialog>
  <el-dialog
    v-model="isShowDialogAuth"
    title="用户认证"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    :show-close="false"
  >
    <el-form
      ref="authFormRef"
      :model="authFormContent"
      :rules="rules"
      status-icon
      label-width="80px"
      @submit.prevent
    >
      <el-form-item
        label="认证码"
        prop="code"
      >
        <el-input
          v-model="authFormContent.code"
          type="password"
          autocomplete="off"
          :validate-event="false"
          show-password
          placeholder="在此输入认证码"
        />
      </el-form-item>
      <el-form-item
        label="监听"
        prop="target"
      >
        <el-select
          v-model="authFormContent.target"
          placeholder="选择"
          :validate-event="false"
        >
          <el-option
            v-for="item in ridOptions"
            :key="item.label"
            :label="item.label"
            :value="item.value"
            :disabled="item.disabled"
            :default-first-option="false"
          />
        </el-select>
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button
        primary
        @click="submitAuthForm(authFormRef)"
      >
        认证
      </el-button>
    </template>
  </el-dialog>
  <el-dialog
    v-model="isShowDialogClosed"
    :show-close="false"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
  >
    <el-result
      icon="warning"
      title="直播间已关闭"
    >
      <template #extra>
        <el-button
          type="primary"
          :disabled="!isPassedOneMinute"
          @click="onClickImmediatelyReconnect"
        >
          重连
        </el-button>
        <el-button
          type="danger"
          @click="onClickQuitExplicitly"
        >
          关闭
        </el-button>
      </template>
    </el-result>
  </el-dialog>
  <el-dialog
    v-model="isShowDialogConnectionLost"
    :show-close="false"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
  >
    <el-result
      icon="error"
      title="连接丢失"
      sub-title="程序将于15秒后尝试重连"
    >
      <template #extra>
        <el-button
          type="primary"
          @click="onClickImmediatelyReconnect"
        >
          立即重连
        </el-button>
      </template>
    </el-result>
  </el-dialog>
</template>

<style lang="scss" scoped>
@import '@/global/styles/themes/index.scss';

.monitor {
  width: 100%;
  height: 100%;
  overflow: hidden;
  font-size: v-bind(fontSizeStyle);
  user-select: none;
  -webkit-app-region: no-drag;
}

.mb-1 {
  margin-bottom: 0.25rem /* 4px */;
}

.bg {
  background-color: v-bind(bgColorValue);
}

.no-drag {
  -webkit-app-region: no-drag;
}

.select-none {
  user-select: none;
}

.w-32 {
  width: 8rem /* 128px */;
}

.mr-3 {
  margin-right: 0.75rem /* 12px */;
}

.ml-2 {
  margin-left: 0.5rem; /* 8px */
}

.mr-2 {
  margin-right: 0.5rem; /* 8px */
}

.align-middle {
  vertical-align: middle;
}

.text-xs {
  font-size: 0.75rem; /* 12px */
  line-height: 1rem; /* 16px */
}

.flex-auto {
  flex: 1 1 auto;
}
</style>
