{
  "compilerOptions": {
    "composite": true,
    "target": "ESNext",
    "jsx": "preserve",
    "lib": ["ESNext", "DOM", "DOM.Iterable"],

    "baseUrl": ".",
    "module": "ESNext",

    /* Bundler mode */
    "moduleResolution": "node",
    "paths": {
      "@/*": ["src/renderer/src/*"]
    },
    "resolveJsonModule": true,
    "allowImportingTsExtensions": true,
    "allowJs": true,

    /* Linting */
    "strict": true,
    "noFallthroughCasesInSwitch": true,
    "noUnusedLocals": true,
    "noUnusedParameters": true,
    "noEmit": true,
    // for .tsbuildinfo file
    "outDir": ".tsc",
    "isolatedModules": true,
    "skipLibCheck": true
  },
  "include": [
    "src/renderer/**/*.d.ts",
    "src/renderer/src/**/*",
    "src/renderer/src/**/*.vue"
  ]
}
