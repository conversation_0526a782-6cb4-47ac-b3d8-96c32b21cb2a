import type { MatchParams } from '@/shared/types'

class WebSocketConnection {
  ws: WebSocket | null = null
  constructor(
    rid: string,
    token: string,
    callback: (msg: string) => void,
    callback_error: () => void,
    callback_close: () => void,
  ) {
    this.ws = new WebSocket(
      // 'ws://localhost:8888',
      'ws://************:11061',
    )
    this.ws.onopen = () => {
      setTimeout(() => {
        this.ws?.send(`${token}:${rid}`)
      }, 1000)
    }
    this.ws.onmessage = (e) => {
      if (typeof e.data === 'string') {
        callback(e.data)
        return
      }
      const reader = new FileReader()
      reader.readAsText(e.data)
      reader.onload = () => {
        const arr = String(reader.result).split('\0') // Packet-Spliting
        for (let i = 0; i < arr.length; i++) {
          callback(arr[i])
        }
      }
    }
    this.ws.onclose = callback_close
    this.ws.onerror = callback_error
  }

  close() {
    this.ws?.close()
  }

  send(msg: MatchParams<WebSocket['send']>) {
    this.ws?.send(msg)
  }
}

export { WebSocketConnection }
