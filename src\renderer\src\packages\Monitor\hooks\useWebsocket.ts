import type { Ref } from 'vue'

import { useIpc } from './useIpc'
import { WebSocketConnection } from '@/global/utils/websocket'
import { STT } from '@/global/utils/stt'
import { getStrMiddle } from '@/global/utils'
import type {
  ChatMsg,
  GiftMsg,
  MonitorOptions,
  MsgBase,
  UserEnterMsg,
  UserEntity,
} from '@/shared/types'

const { invokeMainEvent } = useIpc()

function isChat(_data: MsgBase): _data is ChatMsg {
  return true
}

function isGift(_data: MsgBase): _data is GiftMsg {
  return true
}

enum EventIndex {
  chat = '弹幕',
  gift = '礼物',
  sp = '特殊事件',
}

export function useWebsocket(
  options: Ref<MonitorOptions>,
  vipList: Ref<UserEntity[]>,
  banList: Ref<UserEntity[]>,
  logging: (
    str: string,
    pathObj: {
      path: string
      fileName: string
    },
  ) => Promise<void>,
) {
  let ws: WebSocketConnection | null = null
  const stt = new STT()
  const danmakuList = ref<ChatMsg[]>([])
  const danmakuListVIP = ref<ChatMsg[]>([])
  const giftList = ref<GiftMsg[]>([])
  const giftListUnfiltered = ref<GiftMsg[]>([])

  // 根据日期以及父目录生成路径与文件名
  const getPathObj = (index: string) => {
    const date = new Date()
    const dateStr = `${String(date.getFullYear())}-${String(
      date.getMonth() + 1,
    )}-${String(date.getDate())}`
    const path = `${options.value.log.dir}\\${dateStr}`
    const fileName = `${dateStr}_${index}.txt`
    return { path, fileName }
  }

  // 生成日志消息本体
  const getMsgStruc = <T extends MsgBase>(data: T, index: EventIndex) => {
    switch (index) {
      case EventIndex.chat:
        if (isChat(data)) {
          const arr = [
            data.dt,
            ' - ',
            data.nn,
            '(',
            data.uid,
            ')',
            ': ',
            data.txt,
          ]
          return ''.concat(...arr)
        }
        else {
          return
        }
      // break not required
      case EventIndex.gift:
        if (isGift(data)) {
          const arr = [
            data.dt,
            ' - ',
            '用户名: ',
            data.nn,
            '(',
            data.uid,
            ')',
            ' / ',
            '礼物ID: ',
            data.gfid,
            ' / ',
            '礼物名: ',
            data.gfname,
            ' / ',
            '接收人: ',
            data.to,
            ' / ',
            '礼物价值: ',
            data.price,
            ' / ',
            '礼物数量: ',
            data.gfcnt,
          ]
          return ''.concat(...arr)
        }
        break
      default:
        break
    }
  }

  // 记录弹幕信息到本地文件
  const logToLocalFile = async <T extends MsgBase>(
    data: T,
    index: EventIndex,
  ) => {
    const pathObj = getPathObj(index)
    const strToWrite = getMsgStruc(data, index)
    if (strToWrite)
      await logging(strToWrite, pathObj)
  }

  const checkKeyWordsMatch = (target: string, keywords: string) => {
    const keywordsTrimmed = keywords ? keywords.trim() : ''
    if (keywordsTrimmed !== '') {
      const arr = keywordsTrimmed.split(' ')
      for (let i = 0; i < arr.length; i++) {
        if (arr[i] !== '' && target === arr[i]) {
          return true
        }
      }
    }
    return false
  }

  const checkKeyWordsFuzzyMatch = (target: string, keywords: string) => {
    const keywordsTrimmed = keywords ? keywords.trim() : ''
    if (keywordsTrimmed !== '') {
      const arr = keywordsTrimmed.split(' ')
      for (let i = 0; i < arr.length; i++) {
        if (arr[i] !== '' && target.includes(arr[i])) {
          return true
        }
      }
    }
    return false
  }

  const testAndUpdateUserWatch = (
    data: ChatMsg | UserEnterMsg,
    userList: UserEntity[],
  ) => {
    let foundIndex = -1

    const isFound = userList.some((el, index) => {
      const userIdMatches = el.userId === data.uid
      const userSecUidMatches = el.userSecUid === data.secuid

      if (userIdMatches || userSecUidMatches) {
        foundIndex = index
        return true
      }

      return false
    })

    if (isFound && foundIndex !== -1) {
      const el = userList[foundIndex]
      const userNameMatches = el.userName === data.nn
      const userIdMatches = el.userId === data.uid
      const userSecUidMatches = el.userSecUid === data.secuid

      if (
        !userNameMatches
        && (userIdMatches || userSecUidMatches)
        && !/神秘人\d{6}/.test(data.nn)
      ) {
        // Update lastKnownAs and userName
        userList[foundIndex].lastKnownAs = el.userName
        userList[foundIndex].userName = data.nn
      }
    }
    const StrippedUserName
      = foundIndex === -1 ? data.nn : userList[foundIndex].userName

    return [isFound, StrippedUserName] as const
  }

  const checkDanmakuValid = (data: ChatMsg) => {
    // 判断关键词
    const keywords = options.value.danmaku.filter.keywords
    if (checkKeyWordsFuzzyMatch(data.txt, keywords))
      return false

    // 判断关键昵称/uid/secid
    if (testAndUpdateUserWatch(data, banList.value)[0])
      return false

    return true
  }

  // 判断是否特别关注
  const checkDanmakuIsVIP = (data: ChatMsg | UserEnterMsg) => {
    return testAndUpdateUserWatch(data, vipList.value)
  }

  const checkGiftValid = (data: GiftMsg, threshold: number) => {
    // 屏蔽关键词
    const keywords = options.value.gift.filter.keywords
    if (checkKeyWordsMatch(data.gfname, keywords))
      return false

    if (Number(data.price) < threshold) {
      // 判断连击或捆绑是否总值小于阈值, 如是, 则抛弃该礼物
      if (Number(data.price) * Number(data.hits) < threshold) {
        const edgeCases = options.value.gift.edgeCases
        if (checkKeyWordsMatch(data.gfname, edgeCases))
          return true
        else return false
      }
    }

    return true
  }

  const getTimeDifference = (time1: string, time2: string) => {
    const [h1, m1, s1] = time1.split(':').map(Number)
    const [h2, m2, s2] = time2.split(':').map(Number)

    const timeInSec1 = h1 * 3600 + m1 * 60 + s1
    const timeInSec2 = h2 * 3600 + m2 * 60 + s2

    return Math.abs(timeInSec1 - timeInSec2)
  }

  const handleNormalGifts = (data: GiftMsg) => {
    const isAboveDropThreshold = checkGiftValid(
      data,
      options.value.gift.filter.drop,
    )
    if (!isAboveDropThreshold)
      return

    const obj = {
      ...data,
      bid: data.bid ? data.bid : '0', // 批量标识符
    }

    if (obj.gfcnt !== '1' && obj.hits === '1') {
      obj.hits = obj.gfcnt
    }

    const isPriceValidForMainPane = checkGiftValid(
      obj,
      options.value.gift.filter.price,
    )
    const targetList = isPriceValidForMainPane
      ? giftList.value
      : giftListUnfiltered.value

    logToLocalFile(obj, EventIndex.gift)

    // UNTESTED

    const dIndex = targetList.findLastIndex((item) => {
      return (
        item.nn === obj.nn
        && item.gfid === obj.gfid
        && item.to === obj.to
        && item.bid === obj.bid
      )
    })
    if (dIndex !== -1) {
      const dItem = targetList[dIndex]

      const shouldMerge
        = dItem.bid !== '0' || getTimeDifference(dItem.dt, obj.dt) <= 10

      if (shouldMerge) {
        targetList.splice(dIndex, 1)
        obj.hits
          = dItem.bid === '0'
            ? String(Number(dItem.hits) + Number(obj.hits))
            : obj.hits
      }
    }

    if (targetList.length + 1 > options.value.threshold) {
      targetList.shift()
    }
    targetList.push(obj)
  }

  const closeWs = () => {
    ws?.close()
    ws = null
  }

  const handleMsg = (msg: string) => {
    if (msg === 'connected') {
      window.dispatchEvent(new Event('wsconnected'))
      invokeMainEvent('request-update')
      return
    }
    else if (msg === 'rejected') {
      ws = null
      window.dispatchEvent(new Event('wsrejected'))
    }
    else if (msg === 'closed') {
      closeWs()
      window.dispatchEvent(new Event('stream-closed'))
    }
    const msgType = getStrMiddle(msg, 'type@=', '/')
    if (!msgType) {
      return
    }

    if (msgType === 'chatmsg') {
      const data = stt.deserialize<ChatMsg>(msg)
      logToLocalFile(data, EventIndex.chat)
      if (!checkDanmakuValid(data)) {
        return
      }
      const obj: ChatMsg = {
        nn: data.nn, // 昵称
        uid: data.uid,
        secuid: data.secuid,
        lvl: data.lvl,
        txt: data.txt, // 弹幕内容
        key: data.key,
        dt: data.dt,
      }
      if (danmakuList.value.length + 1 > options.value.threshold) {
        danmakuList.value.shift()
      }
      danmakuList.value.push(obj)
      const [isFound, userName] = checkDanmakuIsVIP(data)
      if (isFound) {
        if (userName !== data.nn)
          obj.nn = `${data.nn}(${userName})`
        if (danmakuListVIP.value.length + 1 > options.value.threshold) {
          danmakuListVIP.value.shift()
        }
        danmakuListVIP.value.push(obj)
      }
    }
    if (msgType === 'dgb') {
      const data = stt.deserialize<GiftMsg>(msg)
      handleNormalGifts(data)
    }
    if (msgType === 'uenter') {
      const data = stt.deserialize<UserEnterMsg>(msg)
      const [isFound, userName] = checkDanmakuIsVIP(data)
      if (!isFound) {
        return
      }
      const obj = {
        ...data,
        txt: '「 进入了直播间 」', // 弹幕内容
      }
      if (danmakuListVIP.value.length + 1 > options.value.threshold) {
        danmakuListVIP.value.shift()
      }
      if (userName !== data.nn)
        obj.nn = `${data.nn}(${userName})`
      danmakuListVIP.value.push(obj)
    }
  }

  const connectWs = (rid: string, token: string) => {
    if (ws) {
      window.dispatchEvent(new Event('wsexists'))
      return
    }

    if (rid === '') {
      return
    }
    ws = new WebSocketConnection(
      rid,
      token,
      (msg) => {
        handleMsg(msg)
      },
      () => {},
      () => {
        ws = null
        window.dispatchEvent(new Event('wserror'))
      },
    )
  }

  onBeforeUnmount(() => {
    closeWs()
  })

  return {
    connectWs,
    closeWs,
    danmakuList,
    danmakuListVIP,
    giftList,
    giftListUnfiltered,
  }
}
