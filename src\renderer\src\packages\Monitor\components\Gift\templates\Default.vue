<script setup lang="ts">
import type { GiftMsg } from '@/shared/types'

const props = defineProps<{
  data: GiftMsg
  highlightThreshold: number
  showSinglePrice: boolean
  showTotalPrice: boolean
  recipientName: string
  showHighlightRecipient: boolean
  showHighlightPrice: boolean
  giftEdgeCases: string
}>()

const giftMsg = computed(() => {
  return `${props.data.gfname}*${props.data.hits} -> `
})

const priceMsg = computed(() => {
  if (props.showSinglePrice && !props.showTotalPrice) {
    return `(${Number(props.data.price)})`
  }
  else if (props.showSinglePrice && props.showTotalPrice) {
    return `(${Number(props.data.price)}/${
      Number(props.data.price) * Number(props.data.hits)
    })`
  }
  else if (!props.showSinglePrice && props.showTotalPrice) {
    return `(${Number(props.data.price) * Number(props.data.hits)})`
  }
  else {
    return ''
  }
})

function getItemClass(item: GiftMsg) {
  if (
    props.showHighlightPrice
    && Number(props.data.price) * Number(item.hits) > props.highlightThreshold
  ) {
    return 'highlight'
  }
  if (props.showHighlightRecipient && item.to.trim() === props.recipientName) {
    return 'highlight'
  }
  if (props.giftEdgeCases.trim().split(' ').includes(item.gfname)) {
    return 'highlight_special'
  }
  return ''
}
</script>

<template>
  <div :class="`item ${getItemClass(data)}`">
    <span class="time_stamp">{{ data.dt }}</span>
    <span
      v-if="data.lvl !== '0'"
      class="item__level"
    >[Lv.{{ data.lvl }}]</span>
    <div class="item__name">
      {{ data.nn }}
    </div>
    <div class="item__cnt">
      {{ giftMsg }} <span class="item__name">{{ data.to }}</span>
      <span>{{ priceMsg }}</span>
    </div>
  </div>
</template>

<style lang="scss" scoped>
@import '@/global/styles/themes/index.scss';

.time_stamp {
  color: black;
  vertical-align: middle;
}

.item {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  margin-bottom: 5px;
  justify-content: flex-start;
  text-align: left;

  &:first-child {
    margin-top: 5px;
  }
  > * {
    margin-right: 5px;
  }
  .item__gift {
    img {
      border-radius: 10%;
    }
  }
  .item__fans {
    width: 60px;
    position: relative;
  }
  .item__level {
    vertical-align: middle;
    @include fontColor('nicknameColor');
  }
  .item__avatar {
    vertical-align: middle;
    display: inline-block;
  }
  .item__name {
    vertical-align: middle;
    @include fontColor('nicknameColor');
  }
  .item__cnt {
    vertical-align: middle;
    @include fontColor('contentColor');
  }
  .item__hits {
    @include fontColor('contentColor');
  }
}

.highlight {
  background-color: rgba(255, 172, 88, 0.3);
  border-top: 1px solid #ffe4b8;
  border-bottom: 1px solid #ffe4b8;
}
.highlight_special {
  background-color: rgba(88, 255, 138, 0.3);
  border-top: 1px solid #deffb8;
  border-bottom: 1px solid #deffb8;
}
</style>
